/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.c
 * @description: WebSocket音频流处理实现
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-07-29
 */

// 标准库头文件
#include <string.h>
#include <stdlib.h>

// ESP-IDF头文件
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

// 项目基础头文件（按依赖层次排序）
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_audio_stream.h"
#include "sk_websocket.h"
#include "sk_audio_buffer.h"
#include "sk_opus_dec.h"

#define TAG "AudioStream"

// 配置参数
#define AUDIO_STREAM_BUFFER_COUNT   16      // 音频缓冲区数量
#define AUDIO_STREAM_FRAME_SIZE     960     // 16kHz, 60ms帧大小
#define AUDIO_STREAM_TIMEOUT_MS     100     // 超时时间
#define AUDIO_STREAM_MAX_SEQ_GAP    10      // 最大序列号间隔

// 音频流控制结构体
typedef struct {
    SkAudioStreamState_e state;             // 当前状态
    uint16_t expectedSeq;                   // 期望的序列号
    uint16_t bufferCount;                   // 当前缓冲区数量（用于统计）
    SemaphoreHandle_t mutex;                // 互斥锁

    // 音频处理相关 - 使用现有封装
    SkOpusDecHandler opusDecoder;           // 使用现有Opus解码器

    // 统计信息
    SkAudioStreamStats_t stats;
} SkAudioStreamCtrl_t;

// 全局控制结构体
static SkAudioStreamCtrl_t g_audioStreamCtrl = {0};

// 内部函数声明
static void SkAudioStreamStartInternal(SkAudioStreamCtrl_t *ctrl);
static void SkAudioStreamStopInternal(SkAudioStreamCtrl_t *ctrl);

// 内部函数声明
static void SkAudioStreamProcessAudioData(SkAudioStreamCtrl_t *ctrl, 
                                         SkWsBinaryHeader_t *header, 
                                         uint16_t totalLen);
static void SkAudioStreamProcessControlData(SkAudioStreamCtrl_t *ctrl,
                                           SkWsBinaryHeader_t *header,
                                           uint16_t totalLen);
static bool SkAudioStreamValidateSequence(SkAudioStreamCtrl_t *ctrl, uint16_t seqNum);
static void SkAudioStreamResetStats(SkAudioStreamCtrl_t *ctrl);

int32_t SkAudioStreamInit() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    // 检查是否已初始化
    if (ctrl->mutex != NULL) {
        SK_LOGI(TAG, "Audio stream already initialized");
        return SK_RET_SUCCESS;
    }
    
    // 创建互斥锁
    ctrl->mutex = xSemaphoreCreateMutex();
    if (ctrl->mutex == NULL) {
        SK_LOGE(TAG, "Failed to create mutex");
        return SK_RET_FAIL;
    }
    
    // 获取现有的Opus解码器句柄
    ctrl->opusDecoder = SkOpusDecGetHandler();
    if (ctrl->opusDecoder == NULL) {
        SK_LOGE(TAG, "Failed to get Opus decoder handler");
        vSemaphoreDelete(ctrl->mutex);
        ctrl->mutex = NULL;
        return SK_RET_FAIL;
    }

    // 初始化参数
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;
    ctrl->expectedSeq = 0;
    ctrl->bufferCount = 0;
    
    // 重置统计信息
    SkAudioStreamResetStats(ctrl);
    
    SK_LOGI(TAG, "Audio stream initialized successfully");
    return SK_RET_SUCCESS;
}

void SkAudioStreamDeinit() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;  // 未初始化
    }
    
    // 停止音频流
    SkAudioStreamStop();
    
    // 清理资源 - 不需要释放Opus解码器，它是全局共享的
    ctrl->opusDecoder = NULL;
    
    vSemaphoreDelete(ctrl->mutex);
    ctrl->mutex = NULL;
    
    SK_LOGI(TAG, "Audio stream deinitialized");
}

void SkAudioStreamOnBinaryData(void *arg, void *data, uint16_t len) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkWsBinaryHeader_t *header = (SkWsBinaryHeader_t *)data;

    // 检查初始化状态
    if (ctrl->mutex == NULL) {
        SK_LOGE(TAG, "Audio stream not initialized - calling SkAudioStreamInit()");
        if (SkAudioStreamInit() != SK_RET_SUCCESS) {
            SK_LOGE(TAG, "Failed to initialize audio stream");
            return;
        }
        SK_LOGI(TAG, "Audio stream initialized successfully");
    }
    
    // 检查数据长度
    if (len < sizeof(SkWsBinaryHeader_t)) {
        SK_LOGE(TAG, "Invalid data length: %d", len);
        return;
    }
    
    // 检查协议版本
    if (header->version != SK_WS_VERSION) {
        SK_LOGE(TAG, "Invalid version: 0x%02x", header->version);
        return;
    }
    
    // 根据数据类型使用不同的互斥锁超时时间
    uint32_t timeout_ms = (header->type == SK_WS_DATA_TYPE_AUDIO_CONTROL) ? 100 : 10;

    // 获取互斥锁
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(timeout_ms)) != pdTRUE) {
        SK_LOGI(TAG, "Mutex busy, skipping data processing (type=0x%02x, timeout=%dms)",
                header->type, timeout_ms);
        return;
    }

    // 根据数据类型处理
    switch (header->type) {
        case SK_WS_DATA_TYPE_AUDIO:
            SkAudioStreamProcessAudioData(ctrl, header, len);
            break;

        case SK_WS_DATA_TYPE_AUDIO_CONTROL:
            SK_LOGI(TAG, "Processing control command with mutex acquired");
            SkAudioStreamProcessControlData(ctrl, header, len);
            break;
            
        default:
            SK_LOGD(TAG, "Unknown data type: 0x%02x", header->type);
            break;
    }
    
    // 释放互斥锁
    xSemaphoreGive(ctrl->mutex);
}

static void SkAudioStreamProcessAudioData(SkAudioStreamCtrl_t *ctrl,
                                         SkWsBinaryHeader_t *header,
                                         uint16_t totalLen) {
    // 检查流状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_STREAMING) {
        SK_LOGI(TAG, "Not streaming (state=%d), ignore audio data seq=%d",
                ctrl->state, header->seqNum);
        return;
    }

    SK_LOGI(TAG, "Processing audio data: seq=%d, len=%d, state=%d",
            header->seqNum, header->payloadLen, ctrl->state);
    
    uint16_t payloadLen = header->payloadLen;
    uint8_t *opusData = header->data;
    
    // 检查数据完整性
    if (totalLen < sizeof(SkWsBinaryHeader_t) + payloadLen) {
        SK_LOGE(TAG, "Incomplete audio data");
        ctrl->stats.lostFrames++;
        return;
    }
    
    // 序列号验证
    if (!SkAudioStreamValidateSequence(ctrl, header->seqNum)) {
        return;  // 序列号验证失败
    }
    
    // 使用现有的Opus解码器封装直接播放
    int32_t result = SkOpusDecPlayRemote(
        ctrl->opusDecoder,
        header->seqNum,
        opusData,
        payloadLen,
        NULL
    );

    if (result == SK_RET_SUCCESS) {
        ctrl->stats.totalFrames++;
        SK_LOGI(TAG, "Opus decode success: seq=%d, size=%d, total_frames=%d",
                header->seqNum, payloadLen, ctrl->stats.totalFrames);
    } else {
        SK_LOGE(TAG, "Opus decode failed: seq=%d, result=%d, lost_frames=%d",
                header->seqNum, result, ctrl->stats.lostFrames + 1);
        ctrl->stats.lostFrames++;
    }
}

static void SkAudioStreamProcessControlData(SkAudioStreamCtrl_t *ctrl,
                                           SkWsBinaryHeader_t *header,
                                           uint16_t totalLen) {
    if (header->payloadLen < 1) {
        SK_LOGE(TAG, "Invalid control data length");
        return;
    }
    
    uint8_t cmd = header->data[0];

    SK_LOGI(TAG, "Received control command: 0x%02x (current_state=%d)",
            cmd, ctrl->state);
    
    switch (cmd) {
        case SK_WS_AUDIO_CMD_START:
            SK_LOGI(TAG, "Processing START command");
            SkAudioStreamStartInternal(ctrl);
            SK_LOGI(TAG, "START command processed, new state=%d", ctrl->state);
            break;

        case SK_WS_AUDIO_CMD_STOP:
            SK_LOGI(TAG, "Processing STOP command");
            SkAudioStreamStopInternal(ctrl);
            SK_LOGI(TAG, "STOP command processed, new state=%d", ctrl->state);
            break;

        case SK_WS_AUDIO_CMD_PAUSE:
            SK_LOGI(TAG, "Processing PAUSE command");
            SkAudioStreamPause();
            SK_LOGI(TAG, "PAUSE command processed, new state=%d", ctrl->state);
            break;

        case SK_WS_AUDIO_CMD_RESUME:
            SK_LOGI(TAG, "Processing RESUME command");
            SkAudioStreamResume();
            SK_LOGI(TAG, "RESUME command processed, new state=%d", ctrl->state);
            break;

        default:
            SK_LOGW(TAG, "Unknown control command: 0x%02x", cmd);
            break;
    }
}

static bool SkAudioStreamValidateSequence(SkAudioStreamCtrl_t *ctrl, uint16_t seqNum) {
    // 检查序列号
    if (seqNum != ctrl->expectedSeq) {
        uint16_t gap = (seqNum > ctrl->expectedSeq) ? 
                      (seqNum - ctrl->expectedSeq) : 
                      (0xFFFF - ctrl->expectedSeq + seqNum + 1);
        
        if (gap > AUDIO_STREAM_MAX_SEQ_GAP) {
            SK_LOGW(TAG, "Sequence gap too large: expected %d, got %d, gap %d", 
                    ctrl->expectedSeq, seqNum, gap);
            ctrl->stats.lostFrames += gap;
        } else if (gap > 1) {
            SK_LOGW(TAG, "Sequence gap: expected %d, got %d, gap %d", 
                    ctrl->expectedSeq, seqNum, gap);
            ctrl->stats.lostFrames += (gap - 1);
        } else if (gap == 0) {
            SK_LOGD(TAG, "Duplicate frame: %d", seqNum);
            return false;  // 重复帧，丢弃
        }
        
        if (seqNum < ctrl->expectedSeq) {
            ctrl->stats.outOfOrderFrames++;
        }
    }
    
    ctrl->expectedSeq = seqNum + 1;
    return true;
}

// 内部函数：不获取互斥锁的启动函数
static void SkAudioStreamStartInternal(SkAudioStreamCtrl_t *ctrl) {
    if (ctrl->state == SK_AUDIO_STREAM_STATE_STREAMING) {
        SK_LOGI(TAG, "Already in streaming state");
        return;  // 已经在流状态
    }

    ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;
    ctrl->expectedSeq = 0;
    ctrl->bufferCount = 0;

    // 使用现有封装重置Opus解码器状态
    SkOpusDecStop();

    // 重置统计信息
    SkAudioStreamResetStats(ctrl);

    SK_LOGI(TAG, "Audio streaming started - state=%d, expected_seq=%d",
            ctrl->state, ctrl->expectedSeq);
}

void SkAudioStreamStart() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->mutex == NULL) {
        SK_LOGE(TAG, "Audio stream not initialized");
        return;
    }

    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(10)) != pdTRUE) {
        SK_LOGD(TAG, "Mutex busy, skipping start operation");
        return;
    }

    SkAudioStreamStartInternal(ctrl);

    xSemaphoreGive(ctrl->mutex);
}

// 内部函数：不获取互斥锁的停止函数
static void SkAudioStreamStopInternal(SkAudioStreamCtrl_t *ctrl) {
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;

    // 停止Opus解码器的远程播放
    SkOpusDecStop();

    ctrl->bufferCount = 0;

    SK_LOGI(TAG, "Audio streaming stopped - state=%d, total_frames=%d, lost_frames=%d",
            ctrl->state, ctrl->stats.totalFrames, ctrl->stats.lostFrames);
}

void SkAudioStreamStop() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->mutex == NULL) {
        return;
    }

    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(10)) != pdTRUE) {
        SK_LOGD(TAG, "Mutex busy, skipping stop operation");
        return;
    }

    SkAudioStreamStopInternal(ctrl);

    xSemaphoreGive(ctrl->mutex);
}

void SkAudioStreamPause() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(10)) != pdTRUE) {
        SK_LOGD(TAG, "Mutex busy, skipping pause operation");
        return;
    }
    
    if (ctrl->state == SK_AUDIO_STREAM_STATE_STREAMING) {
        ctrl->state = SK_AUDIO_STREAM_STATE_PAUSED;
        SK_LOGI(TAG, "Audio streaming paused");
    }
    
    xSemaphoreGive(ctrl->mutex);
}

void SkAudioStreamResume() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(10)) != pdTRUE) {
        SK_LOGD(TAG, "Mutex busy, skipping resume operation");
        return;
    }
    
    if (ctrl->state == SK_AUDIO_STREAM_STATE_PAUSED) {
        ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;
        SK_LOGI(TAG, "Audio streaming resumed");
    }
    
    xSemaphoreGive(ctrl->mutex);
}

SkAudioStreamState_e SkAudioStreamGetState() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return SK_AUDIO_STREAM_STATE_IDLE;
    }
    
    return ctrl->state;
}

bool SkAudioStreamIsActive() {
    SkAudioStreamState_e state = SkAudioStreamGetState();
    return (state == SK_AUDIO_STREAM_STATE_STREAMING || 
            state == SK_AUDIO_STREAM_STATE_PAUSED);
}

void SkAudioStreamGetStats(SkAudioStreamStats_t *stats) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (stats == NULL || ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        *stats = ctrl->stats;
        xSemaphoreGive(ctrl->mutex);
    } else {
        SK_LOGD(TAG, "Mutex busy, returning empty stats");
        memset(stats, 0, sizeof(SkAudioStreamStats_t));
    }
}

size_t SkAudioStreamFeedPlayAudio(uint16_t *buff, size_t len,
                                  SkAudioDownlinkTimeRecord *timeRecord) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    SK_LOGI(TAG, "FeedPlayAudio called: len=%d, state=%d, mutex=%p",
            len, (ctrl->mutex ? ctrl->state : -1), ctrl->mutex);

    // 检查初始化状态
    if (ctrl->mutex == NULL) {
        SK_LOGI(TAG, "Audio stream not initialized, using original Opus decoder");
        // 如果音频流未初始化，使用原有的Opus解码器
        return SkOpusDecFeedPlayAudio(buff, len, timeRecord);
    }

    // 检查流状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_STREAMING) {
        SK_LOGI(TAG, "Not in streaming state (%d), using original Opus decoder", ctrl->state);
        // 如果不在流状态，使用原有的Opus解码器
        return SkOpusDecFeedPlayAudio(buff, len, timeRecord);
    }

    SK_LOGI(TAG, "In streaming mode, getting audio from Opus decoder");
    // 在流状态下，直接使用Opus解码器的输出
    size_t result = SkOpusDecFeedPlayAudio(buff, len, timeRecord);

    // 更新统计信息
    if (xSemaphoreTake(ctrl->mutex, 0) == pdTRUE) {
        if (result == 0) {
            ctrl->stats.bufferUnderrun++;
        }
        xSemaphoreGive(ctrl->mutex);
    }

    if (result > 0) {
        SK_LOGI(TAG, "Audio output: %d bytes to speaker (streaming mode)", result);
    } else {
        SK_LOGI(TAG, "No audio data available for output (streaming mode)");
    }

    return result;
}

static void SkAudioStreamResetStats(SkAudioStreamCtrl_t *ctrl) {
    memset(&ctrl->stats, 0, sizeof(SkAudioStreamStats_t));
}