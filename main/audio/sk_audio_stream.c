/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.c
 * @description: WebSocket音频流处理实现
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-07-29
 */

// 标准库头文件
#include <string.h>
#include <stdlib.h>

// ESP-IDF头文件
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

// 项目基础头文件（按依赖层次排序）
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_audio_stream.h"
#include "sk_websocket.h"
#include "sk_audio_buffer.h"
#include "sk_opus_dec.h"

#define TAG "AudioStream"

// 配置参数
#define AUDIO_STREAM_BUFFER_COUNT   16      // 音频缓冲区数量
#define AUDIO_STREAM_FRAME_SIZE     960     // 16kHz, 60ms帧大小
#define AUDIO_STREAM_TIMEOUT_MS     100     // 超时时间
#define AUDIO_STREAM_MAX_SEQ_GAP    10      // 最大序列号间隔

// 音频流控制结构体
typedef struct {
    SkAudioStreamState_e state;             // 当前状态
    uint16_t expectedSeq;                   // 期望的序列号
    uint16_t bufferCount;                   // 当前缓冲区数量
    SemaphoreHandle_t mutex;                // 互斥锁
    
    // 音频处理相关 - 使用现有封装
    void *audioQueue;                       // 使用SkCreateAudioQueue
    SkOpusDecHandler opusDecoder;           // 使用现有Opus解码器
    
    // 统计信息
    SkAudioStreamStats_t stats;
} SkAudioStreamCtrl_t;

// 全局控制结构体
static SkAudioStreamCtrl_t g_audioStreamCtrl = {0};

// 内部函数声明
static void SkAudioStreamProcessAudioData(SkAudioStreamCtrl_t *ctrl, 
                                         SkWsBinaryHeader_t *header, 
                                         uint16_t totalLen);
static void SkAudioStreamProcessControlData(SkAudioStreamCtrl_t *ctrl,
                                           SkWsBinaryHeader_t *header,
                                           uint16_t totalLen);
static bool SkAudioStreamValidateSequence(SkAudioStreamCtrl_t *ctrl, uint16_t seqNum);
static void SkAudioStreamResetStats(SkAudioStreamCtrl_t *ctrl);

int32_t SkAudioStreamInit() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    // 检查是否已初始化
    if (ctrl->mutex != NULL) {
        SK_LOGI(TAG, "Audio stream already initialized");
        return SK_RET_SUCCESS;
    }
    
    // 创建互斥锁
    ctrl->mutex = xSemaphoreCreateMutex();
    if (ctrl->mutex == NULL) {
        SK_LOGE(TAG, "Failed to create mutex");
        return SK_RET_FAIL;
    }
    
    // 使用现有的音频缓冲队列封装
    ctrl->audioQueue = SkCreateAudioQueue(
        AUDIO_STREAM_BUFFER_COUNT, 
        AUDIO_STREAM_FRAME_SIZE * sizeof(int16_t), 
        0
    );
    
    if (ctrl->audioQueue == NULL) {
        SK_LOGE(TAG, "Failed to create audio queue");
        vSemaphoreDelete(ctrl->mutex);
        ctrl->mutex = NULL;
        return SK_RET_FAIL;
    }
    
    // 使用现有的Opus解码器封装
    ctrl->opusDecoder = SkOpusDecInit(16000, 1, 60, NULL);
    if (ctrl->opusDecoder == NULL) {
        SK_LOGE(TAG, "Failed to init Opus decoder");
        SkDesctoryAudioQueue(ctrl->audioQueue);
        vSemaphoreDelete(ctrl->mutex);
        ctrl->mutex = NULL;
        return SK_RET_FAIL;
    }
    
    // 初始化参数
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;
    ctrl->expectedSeq = 0;
    ctrl->bufferCount = 0;
    
    // 重置统计信息
    SkAudioStreamResetStats(ctrl);
    
    SK_LOGI(TAG, "Audio stream initialized successfully");
    return SK_RET_SUCCESS;
}

void SkAudioStreamDeinit() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;  // 未初始化
    }
    
    // 停止音频流
    SkAudioStreamStop();
    
    // 清理资源 - 使用现有封装
    if (ctrl->audioQueue) {
        SkDesctoryAudioQueue(ctrl->audioQueue);
        ctrl->audioQueue = NULL;
    }
    
    if (ctrl->opusDecoder) {
        SkOpusDecDeinit(ctrl->opusDecoder);
        ctrl->opusDecoder = NULL;
    }
    
    vSemaphoreDelete(ctrl->mutex);
    ctrl->mutex = NULL;
    
    SK_LOGI(TAG, "Audio stream deinitialized");
}

void SkAudioStreamOnBinaryData(void *arg, void *data, uint16_t len) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkWsBinaryHeader_t *header = (SkWsBinaryHeader_t *)data;
    
    // 检查初始化状态
    if (ctrl->mutex == NULL) {
        SK_LOGE(TAG, "Audio stream not initialized");
        return;
    }
    
    // 检查数据长度
    if (len < sizeof(SkWsBinaryHeader_t)) {
        SK_LOGE(TAG, "Invalid data length: %d", len);
        return;
    }
    
    // 检查协议版本
    if (header->version != SK_WS_VERSION) {
        SK_LOGE(TAG, "Invalid version: 0x%02x", header->version);
        return;
    }
    
    // 获取互斥锁
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(AUDIO_STREAM_TIMEOUT_MS)) != pdTRUE) {
        SK_LOGW(TAG, "Failed to take mutex");
        return;
    }
    
    // 根据数据类型处理
    switch (header->type) {
        case SK_WS_DATA_TYPE_AUDIO:
            SkAudioStreamProcessAudioData(ctrl, header, len);
            break;
            
        case SK_WS_DATA_TYPE_AUDIO_CONTROL:
            SkAudioStreamProcessControlData(ctrl, header, len);
            break;
            
        default:
            SK_LOGD(TAG, "Unknown data type: 0x%02x", header->type);
            break;
    }
    
    // 释放互斥锁
    xSemaphoreGive(ctrl->mutex);
}

static void SkAudioStreamProcessAudioData(SkAudioStreamCtrl_t *ctrl, 
                                         SkWsBinaryHeader_t *header, 
                                         uint16_t totalLen) {
    // 检查流状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_STREAMING) {
        SK_LOGD(TAG, "Not streaming, ignore audio data");
        return;
    }
    
    uint16_t payloadLen = header->payloadLen;
    uint8_t *opusData = header->data;
    
    // 检查数据完整性
    if (totalLen < sizeof(SkWsBinaryHeader_t) + payloadLen) {
        SK_LOGE(TAG, "Incomplete audio data");
        ctrl->stats.lostFrames++;
        return;
    }
    
    // 序列号验证
    if (!SkAudioStreamValidateSequence(ctrl, header->seqNum)) {
        return;  // 序列号验证失败
    }
    
    // 使用现有的音频缓冲区封装获取空闲缓冲区
    SkAudioBuf *audioBuf = SkAudioBufferGetFree(ctrl->audioQueue, 10);
    if (audioBuf == NULL) {
        SK_LOGW(TAG, "No free audio buffer available");
        ctrl->stats.bufferOverrun++;
        return;
    }
    
    // 使用现有的Opus解码器封装
    int32_t pcmSamples = SkOpusDecDecode(
        ctrl->opusDecoder,
        opusData,
        payloadLen,
        (int16_t *)(audioBuf->data + audioBuf->offset),
        (audioBuf->size - audioBuf->offset) / sizeof(int16_t)
    );
    
    if (pcmSamples > 0) {
        // 设置缓冲区信息
        audioBuf->length = pcmSamples * sizeof(int16_t);
        audioBuf->sessionId = header->seqNum;
        audioBuf->voiceType = 0;  // 远程音频
        
        // 使用现有封装放入数据队列
        if (SkAudioBufferPutData(ctrl->audioQueue, audioBuf) == SK_RET_SUCCESS) {
            ctrl->bufferCount++;
            ctrl->stats.totalFrames++;
            SK_LOGD(TAG, "Decoded audio frame: seq=%d, samples=%d, buffers=%d", 
                     header->seqNum, pcmSamples, ctrl->bufferCount);
        } else {
            SK_LOGE(TAG, "Failed to put audio buffer");
            SkAudioBufferPutFree(ctrl->audioQueue, audioBuf);
        }
    } else {
        // 解码失败，释放缓冲区
        SkAudioBufferPutFree(ctrl->audioQueue, audioBuf);
        SK_LOGE(TAG, "Opus decode failed: %d", pcmSamples);
        ctrl->stats.lostFrames++;
    }
}

static void SkAudioStreamProcessControlData(SkAudioStreamCtrl_t *ctrl,
                                           SkWsBinaryHeader_t *header,
                                           uint16_t totalLen) {
    if (header->payloadLen < 1) {
        SK_LOGE(TAG, "Invalid control data length");
        return;
    }
    
    uint8_t cmd = header->data[0];
    
    SK_LOGI(TAG, "Received control command: 0x%02x", cmd);
    
    switch (cmd) {
        case SK_WS_AUDIO_CMD_START:
            SkAudioStreamStart();
            break;
            
        case SK_WS_AUDIO_CMD_STOP:
            SkAudioStreamStop();
            break;
            
        case SK_WS_AUDIO_CMD_PAUSE:
            SkAudioStreamPause();
            break;
            
        case SK_WS_AUDIO_CMD_RESUME:
            SkAudioStreamResume();
            break;
            
        default:
            SK_LOGW(TAG, "Unknown control command: 0x%02x", cmd);
            break;
    }
}

static bool SkAudioStreamValidateSequence(SkAudioStreamCtrl_t *ctrl, uint16_t seqNum) {
    // 检查序列号
    if (seqNum != ctrl->expectedSeq) {
        uint16_t gap = (seqNum > ctrl->expectedSeq) ? 
                      (seqNum - ctrl->expectedSeq) : 
                      (0xFFFF - ctrl->expectedSeq + seqNum + 1);
        
        if (gap > AUDIO_STREAM_MAX_SEQ_GAP) {
            SK_LOGW(TAG, "Sequence gap too large: expected %d, got %d, gap %d", 
                    ctrl->expectedSeq, seqNum, gap);
            ctrl->stats.lostFrames += gap;
        } else if (gap > 1) {
            SK_LOGW(TAG, "Sequence gap: expected %d, got %d, gap %d", 
                    ctrl->expectedSeq, seqNum, gap);
            ctrl->stats.lostFrames += (gap - 1);
        } else if (gap == 0) {
            SK_LOGD(TAG, "Duplicate frame: %d", seqNum);
            return false;  // 重复帧，丢弃
        }
        
        if (seqNum < ctrl->expectedSeq) {
            ctrl->stats.outOfOrderFrames++;
        }
    }
    
    ctrl->expectedSeq = seqNum + 1;
    return true;
}

void SkAudioStreamStart() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        SK_LOGE(TAG, "Audio stream not initialized");
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(AUDIO_STREAM_TIMEOUT_MS)) != pdTRUE) {
        SK_LOGW(TAG, "Failed to take mutex");
        return;
    }
    
    if (ctrl->state == SK_AUDIO_STREAM_STATE_STREAMING) {
        xSemaphoreGive(ctrl->mutex);
        return;  // 已经在流状态
    }
    
    ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;
    ctrl->expectedSeq = 0;
    ctrl->bufferCount = 0;
    
    // 使用现有封装重置Opus解码器状态
    SkOpusDecStop();
    
    // 重置统计信息
    SkAudioStreamResetStats(ctrl);
    
    xSemaphoreGive(ctrl->mutex);
    
    SK_LOGI(TAG, "Audio streaming started");
}

void SkAudioStreamStop() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(AUDIO_STREAM_TIMEOUT_MS)) != pdTRUE) {
        SK_LOGW(TAG, "Failed to take mutex");
        return;
    }
    
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;
    
    // 使用现有封装清空音频缓冲区
    SkAudioBuf *audioBuf;
    while ((audioBuf = SkAudioBufferGetData(ctrl->audioQueue, 0)) != NULL) {
        SkAudioBufferPutFree(ctrl->audioQueue, audioBuf);
    }
    
    ctrl->bufferCount = 0;
    
    xSemaphoreGive(ctrl->mutex);
    
    SK_LOGI(TAG, "Audio streaming stopped");
}

void SkAudioStreamPause() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(AUDIO_STREAM_TIMEOUT_MS)) != pdTRUE) {
        return;
    }
    
    if (ctrl->state == SK_AUDIO_STREAM_STATE_STREAMING) {
        ctrl->state = SK_AUDIO_STREAM_STATE_PAUSED;
        SK_LOGI(TAG, "Audio streaming paused");
    }
    
    xSemaphoreGive(ctrl->mutex);
}

void SkAudioStreamResume() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(AUDIO_STREAM_TIMEOUT_MS)) != pdTRUE) {
        return;
    }
    
    if (ctrl->state == SK_AUDIO_STREAM_STATE_PAUSED) {
        ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;
        SK_LOGI(TAG, "Audio streaming resumed");
    }
    
    xSemaphoreGive(ctrl->mutex);
}

SkAudioStreamState_e SkAudioStreamGetState() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->mutex == NULL) {
        return SK_AUDIO_STREAM_STATE_IDLE;
    }
    
    return ctrl->state;
}

bool SkAudioStreamIsActive() {
    SkAudioStreamState_e state = SkAudioStreamGetState();
    return (state == SK_AUDIO_STREAM_STATE_STREAMING || 
            state == SK_AUDIO_STREAM_STATE_PAUSED);
}

void SkAudioStreamGetStats(SkAudioStreamStats_t *stats) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (stats == NULL || ctrl->mutex == NULL) {
        return;
    }
    
    if (xSemaphoreTake(ctrl->mutex, pdMS_TO_TICKS(AUDIO_STREAM_TIMEOUT_MS)) == pdTRUE) {
        *stats = ctrl->stats;
        xSemaphoreGive(ctrl->mutex);
    }
}

size_t SkAudioStreamFeedPlayAudio(uint16_t *buff, size_t len, 
                                  SkAudioDownlinkTimeRecord *timeRecord) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    // 检查初始化状态
    if (ctrl->mutex == NULL) {
        return 0;
    }
    
    // 检查流状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_STREAMING) {
        return 0;  // 没有数据时返回0，播放器会输出静音
    }
    
    // 使用现有封装获取音频数据
    SkAudioBuf *audioBuf = SkAudioBufferGetData(ctrl->audioQueue, 50);
    if (audioBuf == NULL) {
        // 缓冲区空，输出静音
        if (xSemaphoreTake(ctrl->mutex, 0) == pdTRUE) {
            ctrl->stats.bufferUnderrun++;
            xSemaphoreGive(ctrl->mutex);
        }
        return 0;
    }
    
    // 复制音频数据
    size_t copyLen = (audioBuf->length < len) ? audioBuf->length : len;
    memcpy(buff, audioBuf->data + audioBuf->offset, copyLen);
    
    // 使用现有封装释放缓冲区
    SkAudioBufferPutFree(ctrl->audioQueue, audioBuf);
    
    // 更新缓冲区计数
    if (xSemaphoreTake(ctrl->mutex, 0) == pdTRUE) {
        ctrl->bufferCount--;
        xSemaphoreGive(ctrl->mutex);
    }
    
    SK_LOGD(TAG, "Feed audio data: %d bytes, buffer_count=%d", 
             copyLen, ctrl->bufferCount);
    
    return copyLen;
}

static void SkAudioStreamResetStats(SkAudioStreamCtrl_t *ctrl) {
    memset(&ctrl->stats, 0, sizeof(SkAudioStreamStats_t));
}