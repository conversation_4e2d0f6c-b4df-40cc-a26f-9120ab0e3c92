/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: main.c
 * @description: 程序入口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include "sdkconfig.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "sk_os.h"
#include "sk_board.h"
#include "sk_audio.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_sm.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_dfx.h"
#include "sk_log.h"
#include "sk_audio_stream.h"
#include "sk_websocket.h"
#include "sk_test.h"
#include "sk_ota_api.h"

#define RESERVE_MEM_SIZE_PER_BLOCK (2048)

static const char *TAG = "SmartKid";

SkSpeechMapItem g_skSpeechMap[] = {
    {SPEECH_CMD_EVENT_CHAT, "wu kong"},
    {SPEECH_CMD_EVENT_MUSIC, "yin yue"},
    {SPEECH_CMD_EVENT_CONFIG, "pei zhi"},
    {SPEECH_CMD_EVENT_CONFIG, "she zhi"},
    {SPEECH_CMD_EVENT_QUERY, "cha cha"},
    {SPEECH_CMD_EVENT_VOLUP, "sheng ying da yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao gao ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao da ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng dian"},
    {SPEECH_CMD_EVENT_VOLUP, "zai da yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "sheng ying xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao di ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao xiao ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "zai xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da ying liang"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da sheng"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao sheng"},
    {SPEECH_CMD_EVENT_HELP, "qiu zhu"},
    {SPEECH_CMD_EVENT_PAUSE, "zan ting"},
    {SPEECH_CMD_EVENT_CONFIRM, "que ding"},
    {SPEECH_CMD_EVENT_QUIT, "tui chu"},
    {SPEECH_CMD_EVENT_QUIT, "ting zhi"},
    {SPEECH_CMD_EVENT_PREV, "shang yi ge"},
    {SPEECH_CMD_EVENT_NEXT, "xia yi ge"},
    {SPEECH_CMD_EVENT_RESUME, "ji xu"},
    {SPEECH_CMD_EVENT_QUIT, "qu xiao"},
    {SPEECH_CMD_EVENT_INFO, "zhuang tai"},
    {SPEECH_CMD_EVENT_START_DBG, "qi dong"},
    {SPEECH_CMD_EVENT_STOP_DBG, "duan kai"},
    {SPEECH_CMD_EVENT_SLEEP, "dai ji"},
    {SPEECH_CMD_EVENT_CALL, "fu jiao"},
    {SPEECH_CMD_EVENT_CALL, "hu jiao"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu ying"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu yin"},
    {SPEECH_CMD_EVENT_MIC_OFF, "guan bi"},
    {SPEECH_CMD_EVENT_CALL, "qi dong hu jiao"},
    {SPEECH_CMD_EVENT_CALL, "da kai dian hua"},
    {SPEECH_CMD_EVENT_CONFIG, "pei wang"},
};

SkStateHandler g_smHandler;

void SkMainCmdProc(int32_t cmd) {
    if (g_smHandler == NULL) {
        return;
    }
    SkSmSendEvent(g_smHandler, SM_EVENT_CMD, cmd, 0, 0);
    return;
}
#ifndef TESTCASE_ENABLED
static void ReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt, size_t blockSize) {
    for (int i = 0; i < memBlockCnt; i++) {
        // 不能超过配置门限，超过门限从PSRAM中分配，不能起到保留效果。所以分片分配.
        memBlock[i] = malloc(blockSize);
    }

    return;
}

static void FreeReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt) {
    for (int i = 0; i < memBlockCnt; i++) {
        if (memBlock[i] != NULL) {
            free(memBlock[i]);
            memBlock[i] = NULL;
        }
    }

    return;
}
#endif

void app_main(void) {
#ifndef TESTCASE_ENABLED
    uint32_t *memResv[32];
    SkRlinkHandler rlinkHandler;
#endif
    SK_LOGI(TAG, "Debug version at %s %s.", __DATE__, __TIME__);
    SkRledInit();
    SkRledSetEvent(SK_LED_EVENT_INIT);
    SK_OS_MODULE_MEM_STAT("start", false);
#ifndef TESTCASE_ENABLED
    ReserveMemory(memResv, ARRAY_SIZE(memResv), RESERVE_MEM_SIZE_PER_BLOCK);
    SK_OS_MODULE_MEM_STAT("Resv", true);
#endif
    ESP_ERROR_CHECK(SkBspBoardInit(16000, sizeof(uint16_t) * 8));
    SK_OS_MODULE_MEM_STAT("Bsp", true);
#ifndef TESTCASE_ENABLED
    g_smHandler = SkSmInit();
    SkConfigInit();
    SK_OS_MODULE_MEM_STAT("Config", true);
    SkWifiInit();
    SkWifiRegEventCb(SkSmOnWifiEvent);
    SK_OS_MODULE_MEM_STAT("WiFiTask", true);
    SkOtaManagerInit();
    SkOtaRegStateCallback(SkSmOtaOnStateChange);
    SK_OS_MODULE_MEM_STAT("OTA", true);
    SkOpusInit(16000, 1, 60);
    SK_OS_MODULE_MEM_STAT("OpusCodec", true);
    SkSrRegister(g_skSpeechMap, sizeof(g_skSpeechMap) / sizeof(SkSpeechMapItem), SkMainCmdProc);
    SK_OS_MODULE_MEM_STAT("Clink", false);
    SkClinkInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Clink", true);
    rlinkHandler = SkRlinkInit(g_smHandler, sizeof(uint16_t), 512, 800);
    SK_OS_MODULE_MEM_STAT("Rlink", true);
    SkWsInit();
    SkWsStart();
    SkWsSetServerIp("************", 8766);
    SkWsStartConnect();
    SK_OS_MODULE_MEM_STAT("WebSocket", true);
    SK_OS_MODULE_MEM_STAT("Audio", false);
    SkAudioInit(sizeof(uint16_t), 960);
    SK_OS_MODULE_MEM_STAT("AudioStream", false);
    if (SkAudioStreamInit() != SK_RET_SUCCESS) {
        SK_LOGE("Main", "Audio stream init failed");
        return;
    }
    SK_OS_MODULE_MEM_STAT("AudioStream", true);
    SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    SkPlayerSetCallback(SkAudioStreamFeedPlayAudio);
    SkSrSetSendFunc(SkOpusEncEnqueue);
    SkOpusEncSetCallback(SkRlinkFeedReordAudio, (void *)rlinkHandler);
    SkRlinkSetCodedDataCallback(SkOpusDecPlayRemote, SkOpusDecGetHandler());
    SkRlinkSetCodedDataEndCallback(SkOpusDecRemoteDataEnd, SkOpusDecGetHandler());
    SkPeripheralInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Peripheral", true);
    FreeReserveMemory(memResv, ARRAY_SIZE(memResv));
    SK_OS_MODULE_MEM_STAT("Resv-End", true);
    SK_OS_MODULE_MEM_STAT("WifiSta", false);
    SkSmSendEvent(g_smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_INIT_OK, 0, 0);
    vTaskDelay(pdMS_TO_TICKS(20000));
    SK_OS_MODULE_MEM_STAT("WifiSta", true);
#else
    SkTestMain();
#endif
    return;
}
