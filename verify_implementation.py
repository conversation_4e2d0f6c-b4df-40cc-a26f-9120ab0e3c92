#!/usr/bin/env python3
"""
验证WebSocket音频流实现的完整性
"""

import os
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (文件不存在)")
        return False

def check_file_content(file_path, search_text, description):
    """检查文件是否包含特定内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if search_text in content:
                print(f"✓ {description}: 找到 '{search_text}'")
                return True
            else:
                print(f"✗ {description}: 未找到 '{search_text}'")
                return False
    except Exception as e:
        print(f"✗ {description}: 读取文件失败 - {e}")
        return False

def main():
    """主验证函数"""
    print("WebSocket音频流实现验证")
    print("=" * 50)
    
    all_passed = True
    
    # 检查ESP32端文件
    print("\n1. ESP32端文件检查:")
    files_to_check = [
        ("main/include/sk_audio_stream.h", "音频流头文件"),
        ("main/audio/sk_audio_stream.c", "音频流实现文件"),
        ("main/protocol/sk_websocket.c", "WebSocket协议文件"),
        ("main/app/main.c", "主程序文件"),
    ]
    
    for file_path, desc in files_to_check:
        if not check_file_exists(file_path, desc):
            all_passed = False
    
    # 检查服务器端文件
    print("\n2. 服务器端文件检查:")
    server_files = [
        ("tools/audio_stream_server.py", "音频流服务器"),
        ("tools/requirements.txt", "Python依赖文件"),
        ("tools/start_audio_server.sh", "服务器启动脚本"),
        ("tools/generate_test_audio.py", "测试音频生成工具"),
        ("tools/test_audio_stream.py", "测试客户端"),
        ("tools/README_AudioStream.md", "详细文档"),
    ]
    
    for file_path, desc in server_files:
        if not check_file_exists(file_path, desc):
            all_passed = False
    
    # 检查关键代码内容
    print("\n3. 关键代码内容检查:")
    content_checks = [
        ("main/include/sk_audio_stream.h", "SkAudioStreamInit", "音频流初始化函数声明"),
        ("main/audio/sk_audio_stream.c", "SkAudioStreamOnBinaryData", "WebSocket数据处理函数"),
        ("main/protocol/sk_websocket.c", "sk_audio_stream.h", "音频流头文件包含"),
        ("main/app/main.c", "SkAudioStreamInit", "主程序中的音频流初始化"),
        ("tools/audio_stream_server.py", "class AudioStreamServer", "音频流服务器类"),
    ]
    
    for file_path, search_text, desc in content_checks:
        if not check_file_content(file_path, search_text, desc):
            all_passed = False
    
    # 检查WebSocket协议扩展
    print("\n4. WebSocket协议扩展检查:")
    protocol_checks = [
        ("main/include/sk_websocket.h", "SK_WS_DATA_TYPE_AUDIO_CONTROL", "音频控制数据类型"),
        ("main/include/sk_websocket.h", "SK_WS_AUDIO_CMD_START", "音频控制命令"),
        ("main/protocol/sk_websocket.c", "SkAudioStreamOnBinaryData", "音频数据处理调用"),
    ]
    
    for file_path, search_text, desc in protocol_checks:
        if not check_file_content(file_path, search_text, desc):
            all_passed = False
    
    # 检查文件权限
    print("\n5. 文件权限检查:")
    executable_files = [
        "tools/start_audio_server.sh",
        "tools/audio_stream_server.py",
        "tools/generate_test_audio.py",
        "tools/test_audio_stream.py",
    ]
    
    for file_path in executable_files:
        if Path(file_path).exists():
            if os.access(file_path, os.X_OK):
                print(f"✓ {file_path}: 具有执行权限")
            else:
                print(f"✗ {file_path}: 缺少执行权限")
                all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ 所有检查通过！WebSocket音频流系统实现完整。")
        print("\n下一步:")
        print("1. 编译ESP32固件: idf.py build")
        print("2. 启动服务器: cd tools && ./start_audio_server.sh")
        print("3. 生成测试音频: cd tools && python3 generate_test_audio.py")
        print("4. 连接ESP32并测试音频播放")
    else:
        print("✗ 发现问题，请检查上述错误并修复。")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
