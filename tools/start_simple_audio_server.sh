#!/bin/bash

# 简单音频流WebSocket服务器启动脚本

echo "🚀 启动简单音频流WebSocket服务器..."
echo "📍 服务器地址: ws://0.0.0.0:8766"
echo "🔧 功能: 接收和显示ESP32发送的音频数据"
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3"
    echo "💡 请安装Python 3.7或更高版本"
    exit 1
fi

# 检查websockets库是否安装
if ! python3 -c "import websockets" 2>/dev/null; then
    echo "📦 安装websockets库..."
    pip3 install websockets
fi

# 启动服务器
echo "🎯 启动服务器..."
python3 ws_server.py
