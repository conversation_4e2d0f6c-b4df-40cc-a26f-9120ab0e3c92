#!/usr/bin/env python3
"""
测试音频流服务器连接的简单脚本
"""

import asyncio
import websockets
import struct
import time

async def test_connection():
    """测试WebSocket连接"""
    uri = "ws://192.168.3.17:8766"  # 使用您设置的服务器地址
    
    try:
        print(f"尝试连接到 {uri}")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功！")
            
            # 发送测试消息
            test_message = "Hello from ESP32 test client"
            await websocket.send(test_message)
            print(f"📤 发送测试消息: {test_message}")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 收到响应: {response}")
            except asyncio.TimeoutError:
                print("⏰ 等待响应超时（这是正常的，服务器可能不会回复文本消息）")
                
            print("🔗 连接测试完成")
                
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保音频流服务器正在运行")
        print("💡 启动服务器命令: cd tools && ./start_audio_server.sh")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

def create_test_audio_packet(seq_num, data):
    """创建测试音频数据包"""
    version = 0x01
    data_type = 0x01  # SK_WS_DATA_TYPE_AUDIO
    payload_len = len(data)
    reserved = 0
    
    # 打包头部（小端序）
    header = struct.pack('<BBHHH', version, data_type, seq_num, payload_len, reserved)
    return header + data

async def test_audio_protocol():
    """测试音频协议"""
    uri = "ws://192.168.3.17:8766"
    
    try:
        print(f"测试音频协议连接到 {uri}")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功！")
            
            # 发送几个测试音频帧
            for i in range(3):
                # 创建测试音频数据（简单的测试数据）
                test_data = bytes([i % 256] * 100)  # 100字节测试数据
                audio_packet = create_test_audio_packet(i, test_data)
                
                await websocket.send(audio_packet)
                print(f"📤 发送音频帧 {i}, 大小: {len(audio_packet)} 字节")
                
                await asyncio.sleep(0.06)  # 60ms间隔
            
            print("🎵 音频协议测试完成")
            
    except Exception as e:
        print(f"❌ 音频协议测试失败: {e}")

async def main():
    """主函数"""
    print("🔧 WebSocket音频流服务器连接测试")
    print("=" * 50)
    
    print("\n1. 测试基本WebSocket连接...")
    await test_connection()
    
    print("\n2. 测试音频协议...")
    await test_audio_protocol()
    
    print("\n✨ 测试完成！")
    print("\n💡 如果连接失败，请检查：")
    print("   1. 音频流服务器是否正在运行")
    print("   2. IP地址和端口是否正确")
    print("   3. 防火墙设置")

if __name__ == "__main__":
    asyncio.run(main())
