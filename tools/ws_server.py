import asyncio
import struct
import time
from websockets.server import serve

class AudioStreamServer:
    def __init__(self):
        self.clients = set()

    async def handle_client(self, websocket, path):
        """处理WebSocket客户端连接"""
        client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        print(f"✅ 客户端连接: {client_addr}")
        self.clients.add(websocket)

        try:
            async for message in websocket:
                await self.process_message(websocket, message, client_addr)
        except Exception as e:
            print(f"❌ 客户端 {client_addr} 连接错误: {e}")
        finally:
            self.clients.discard(websocket)
            print(f"🔌 客户端断开: {client_addr}")

    async def process_message(self, websocket, message, client_addr):
        """处理接收到的消息"""
        if isinstance(message, str):
            # 文本消息
            print(f"📝 收到文本消息 from {client_addr}: {message}")
            await websocket.send(f"Echo: {message}")
        else:
            # 二进制消息（音频数据）
            await self.process_audio_data(websocket, message, client_addr)

    async def process_audio_data(self, websocket, data, client_addr):
        """处理音频数据"""
        if len(data) < 8:
            print(f"⚠️  数据太短 from {client_addr}: {len(data)} bytes")
            return

        try:
            # 解析音频数据包头部
            version, data_type, seq_num, payload_len, reserved = struct.unpack('<BBHHH', data[:8])
            payload = data[8:]

            print(f"🎵 收到音频数据 from {client_addr}:")
            print(f"   版本: {version}, 类型: {data_type}, 序列号: {seq_num}")
            print(f"   负载长度: {payload_len}, 实际长度: {len(payload)}")

            # 发送确认响应（可选）
            response = f"Audio frame {seq_num} received"
            await websocket.send(response)

        except struct.error as e:
            print(f"❌ 解析音频数据失败 from {client_addr}: {e}")

    async def send_test_audio(self):
        """发送测试音频数据到所有连接的客户端"""
        seq_num = 0
        while True:
            if self.clients:
                # 创建测试音频数据包
                test_data = bytes([seq_num % 256] * 100)  # 100字节测试数据
                audio_packet = self.create_audio_packet(seq_num, test_data)

                # 发送到所有客户端
                disconnected = set()
                for client in self.clients:
                    try:
                        await client.send(audio_packet)
                        print(f"📤 发送测试音频帧 {seq_num} 到客户端")
                    except Exception as e:
                        print(f"❌ 发送失败: {e}")
                        disconnected.add(client)

                # 移除断开的客户端
                self.clients -= disconnected
                seq_num += 1

            await asyncio.sleep(0.06)  # 60ms间隔

    def create_audio_packet(self, seq_num, data):
        """创建音频数据包"""
        version = 0x01
        data_type = 0x01  # SK_WS_DATA_TYPE_AUDIO
        payload_len = len(data)
        reserved = 0

        # 打包头部（小端序）
        header = struct.pack('<BBHHH', version, data_type, seq_num, payload_len, reserved)
        return header + data

async def main():
    server = AudioStreamServer()

    # 启动WebSocket服务器
    print("🚀 启动音频流WebSocket服务器...")
    async with serve(server.handle_client, "0.0.0.0", 8766):
        print("✅ 音频流服务器已在 ws://0.0.0.0:8766 启动")
        print("📡 等待ESP32连接...")
        print("💡 提示：")
        print("   - 服务器会接收和显示音频数据")
        print("   - 可以发送文本消息进行测试")
        print("   - 按 Ctrl+C 停止服务器")

        # 启动测试音频发送任务（可选）
        # asyncio.create_task(server.send_test_audio())

        # 保持服务器运行
        await asyncio.Future()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")