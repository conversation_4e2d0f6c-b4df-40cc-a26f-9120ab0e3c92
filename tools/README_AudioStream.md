# WebSocket音频流系统

## 概述

这个系统实现了通过WebSocket将音频文件实时流式传输到ESP32终端进行播放的功能。

## 系统架构

```
[音频文件] → [Python WebSocket服务器] → [ESP32终端] → [音频播放]
    ↓              ↓                    ↓           ↓
  读取文件      Opus编码/分片         WebSocket接收   Opus解码
                实时发送              缓冲管理       I2S播放
```

## 功能特性

- **实时音频流传输**: 支持将WAV音频文件实时流式传输
- **Opus编解码**: 使用Opus编解码器进行高质量音频压缩
- **缓冲管理**: 智能缓冲区管理，处理网络抖动
- **序列号管理**: 支持丢包检测和乱序处理
- **统计信息**: 提供详细的传输统计信息

## 文件结构

```
tools/
├── audio_stream_server.py      # WebSocket音频流服务器
├── requirements.txt            # Python依赖
├── start_audio_server.sh       # 服务器启动脚本
├── generate_test_audio.py      # 测试音频生成工具
└── README_AudioStream.md       # 本文档

main/
├── include/sk_audio_stream.h   # 音频流接口定义
├── audio/sk_audio_stream.c     # 音频流实现
├── include/sk_websocket.h      # WebSocket协议扩展
├── protocol/sk_websocket.c     # WebSocket协议实现修改
└── app/main.c                  # 主程序集成
```

## 使用方法

### 1. 准备ESP32终端

编译并烧录固件：
```bash
idf.py build
idf.py flash monitor
```

### 2. 启动音频流服务器

```bash
cd tools
chmod +x start_audio_server.sh
./start_audio_server.sh --host 0.0.0.0 --port 8765
```

### 3. 生成测试音频（可选）

```bash
cd tools
python3 generate_test_audio.py --output test.wav --duration 10
```

### 4. 连接和播放

1. 在ESP32终端中设置服务器IP：
   ```c
   SkWsSetServerIp("*************", 8765);  // 替换为实际IP
   SkWsStartConnect();
   ```

2. 在服务器端输入音频文件路径开始播放

## 音频格式要求

- **格式**: WAV文件
- **采样率**: 16kHz
- **位深**: 16位
- **声道**: 单声道

## API接口

### ESP32端接口

```c
// 初始化和反初始化
int32_t SkAudioStreamInit();
void SkAudioStreamDeinit();

// 音频流控制
void SkAudioStreamStart();
void SkAudioStreamStop();
void SkAudioStreamPause();
void SkAudioStreamResume();

// 状态查询
SkAudioStreamState_e SkAudioStreamGetState();
bool SkAudioStreamIsActive();
void SkAudioStreamGetStats(SkAudioStreamStats_t *stats);
```

### 服务器端接口

```python
# 创建服务器
server = AudioStreamServer(host='0.0.0.0', port=8765)

# 流式传输音频文件
await server.stream_audio_file(file_path)

# 停止传输
server.stop_streaming()
```

## 协议定义

### 音频数据包格式

```c
typedef struct {
    uint8_t version;        // 协议版本 (0x01)
    uint8_t type;           // 数据类型 (0x01=音频数据)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 载荷长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus编码数据
} SkWsAudioFrame_t;
```

### 控制命令格式

```c
typedef struct {
    uint8_t version;        // 协议版本 (0x01)
    uint8_t type;           // 数据类型 (0x02=控制命令)
    uint16_t seqNum;        // 序列号 (0)
    uint16_t payloadLen;    // 载荷长度 (1)
    uint16_t resv;          // 保留字段
    uint8_t cmd;            // 控制命令
} SkWsControlFrame_t;
```

### 控制命令定义

- `0x01`: 开始播放
- `0x02`: 停止播放
- `0x03`: 暂停播放
- `0x04`: 恢复播放

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认IP地址和端口正确
   - 检查防火墙设置

2. **音频播放断续**
   - 检查网络带宽
   - 调整缓冲区大小
   - 检查CPU负载

3. **音频格式错误**
   - 确认音频文件格式符合要求
   - 使用generate_test_audio.py生成测试文件

### 调试信息

ESP32端可以通过以下方式获取统计信息：

```c
SkAudioStreamStats_t stats;
SkAudioStreamGetStats(&stats);
SK_LOGI("Debug", "Total: %d, Lost: %d, OutOfOrder: %d", 
        stats.totalFrames, stats.lostFrames, stats.outOfOrderFrames);
```

## 性能优化

1. **网络优化**
   - 使用有线网络连接
   - 减少网络延迟
   - 调整发送速率

2. **缓冲优化**
   - 根据网络状况调整缓冲区大小
   - 实现自适应缓冲策略

3. **CPU优化**
   - 使用DMA传输
   - 优化解码算法
   - 合理分配任务优先级

## 扩展功能

1. **多客户端支持**: 支持同时向多个ESP32设备流式传输
2. **音频效果**: 添加音量控制、均衡器等音频效果
3. **格式支持**: 支持更多音频格式（MP3、AAC等）
4. **实时音频**: 支持麦克风实时音频流传输
