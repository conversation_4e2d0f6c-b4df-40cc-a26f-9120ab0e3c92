# WebSocket音频流系统使用指南

## 系统概述

本系统实现了通过WebSocket将音频文件实时流式传输到ESP32终端进行播放的功能，支持Opus编解码和智能缓冲管理。

## 实现的文件

### ESP32端文件

1. **`main/include/sk_audio_stream.h`** - 音频流接口定义
2. **`main/audio/sk_audio_stream.c`** - 音频流处理实现
3. **`main/include/sk_websocket.h`** - WebSocket协议扩展（添加音频控制命令）
4. **`main/protocol/sk_websocket.c`** - WebSocket协议实现修改
5. **`main/app/main.c`** - 主程序集成

### 服务器端文件

1. **`tools/audio_stream_server.py`** - WebSocket音频流服务器
2. **`tools/requirements.txt`** - Python依赖
3. **`tools/start_audio_server.sh`** - 服务器启动脚本
4. **`tools/generate_test_audio.py`** - 测试音频生成工具
5. **`tools/test_audio_stream.py`** - 测试客户端
6. **`tools/README_AudioStream.md`** - 详细文档

## 快速开始

### 1. 编译ESP32固件

```bash
# 在项目根目录
idf.py build
idf.py flash monitor
```

### 2. 启动音频流服务器

```bash
cd tools

# 给脚本执行权限
chmod +x start_audio_server.sh

# 启动服务器
./start_audio_server.sh --host 0.0.0.0 --port 8765
```

### 3. 生成测试音频

```bash
cd tools
python3 generate_test_audio.py --output test.wav --duration 10
```

### 4. 连接和播放

1. 在ESP32终端中连接WebSocket服务器：
   ```c
   SkWsSetServerIp("*************", 8765);  // 替换为服务器实际IP
   SkWsStartConnect();
   ```

2. 在服务器控制台输入音频文件路径：
   ```
   Enter audio file path (or 'quit' to exit): test.wav
   ```

3. 系统会自动开始音频流传输和播放

## 系统架构

### 数据流

```
音频文件 → Opus编码 → WebSocket分片传输 → ESP32接收 → Opus解码 → I2S播放
```

### 协议格式

#### 音频数据包
```c
struct SkWsAudioFrame {
    uint8_t version;        // 0x01
    uint8_t type;           // 0x01 (音频数据)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 数据长度
    uint16_t resv;          // 保留
    uint8_t data[];         // Opus编码数据
};
```

#### 控制命令包
```c
struct SkWsControlFrame {
    uint8_t version;        // 0x01
    uint8_t type;           // 0x02 (控制命令)
    uint16_t seqNum;        // 0
    uint16_t payloadLen;    // 1
    uint16_t resv;          // 保留
    uint8_t cmd;            // 命令码
};
```

### 控制命令

- `0x01`: 开始播放
- `0x02`: 停止播放
- `0x03`: 暂停播放
- `0x04`: 恢复播放

## 主要功能

### ESP32端功能

1. **音频流接收**: 通过WebSocket接收音频数据
2. **Opus解码**: 实时解码Opus音频数据
3. **缓冲管理**: 智能音频缓冲区管理
4. **序列号处理**: 丢包检测和乱序处理
5. **统计信息**: 传输质量统计

### 服务器端功能

1. **音频文件读取**: 支持WAV格式音频文件
2. **Opus编码**: 实时音频编码
3. **流控制**: 按播放速率控制发送
4. **多客户端**: 支持多个ESP32设备连接
5. **命令控制**: 播放控制命令

## API接口

### ESP32端API

```c
// 初始化
int32_t SkAudioStreamInit();
void SkAudioStreamDeinit();

// 控制
void SkAudioStreamStart();
void SkAudioStreamStop();
void SkAudioStreamPause();
void SkAudioStreamResume();

// 状态查询
SkAudioStreamState_e SkAudioStreamGetState();
bool SkAudioStreamIsActive();
void SkAudioStreamGetStats(SkAudioStreamStats_t *stats);
```

### 服务器端API

```python
# 创建服务器
server = AudioStreamServer(host='0.0.0.0', port=8765)

# 启动服务器
await server.start_server()

# 流式传输
await server.stream_audio_file(file_path)
```

## 配置参数

### ESP32端配置

```c
#define AUDIO_STREAM_BUFFER_COUNT   16      // 缓冲区数量
#define AUDIO_STREAM_FRAME_SIZE     960     // 帧大小(16kHz, 60ms)
#define AUDIO_STREAM_TIMEOUT_MS     100     // 超时时间
#define AUDIO_STREAM_MAX_SEQ_GAP    10      // 最大序列号间隔
```

### 服务器端配置

```python
# 音频参数
sample_rate = 16000     # 采样率
channels = 1            # 声道数
frame_duration = 60     # 帧时长(ms)

# 网络参数
host = '0.0.0.0'        # 监听地址
port = 8765             # 监听端口
```

## 测试和调试

### 1. 连接测试

```bash
cd tools
python3 test_audio_stream.py --test connection
```

### 2. 协议测试

```bash
cd tools
python3 test_audio_stream.py --test protocol
```

### 3. 统计信息查看

在ESP32端可以查看传输统计：

```c
SkAudioStreamStats_t stats;
SkAudioStreamGetStats(&stats);
SK_LOGI("Debug", "Total: %d, Lost: %d, OutOfOrder: %d, Underrun: %d, Overrun: %d", 
        stats.totalFrames, stats.lostFrames, stats.outOfOrderFrames,
        stats.bufferUnderrun, stats.bufferOverrun);
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查IP地址和端口
   - 确认网络连通性
   - 检查防火墙设置

2. **音频断续**
   - 检查网络带宽
   - 调整缓冲区大小
   - 检查CPU负载

3. **编译错误**
   - 确认所有文件都已创建
   - 检查头文件包含路径
   - 验证函数声明和定义匹配

### 调试技巧

1. **启用详细日志**
   ```c
   // 在sk_audio_stream.c中将日志级别改为DEBUG
   SK_LOGD(TAG, "Debug message");
   ```

2. **监控缓冲区状态**
   ```c
   bool active = SkAudioStreamIsActive();
   SkAudioStreamState_e state = SkAudioStreamGetState();
   ```

3. **网络抓包分析**
   ```bash
   # 使用Wireshark或tcpdump分析WebSocket流量
   tcpdump -i any -w audio_stream.pcap port 8765
   ```

## 性能优化

1. **网络优化**
   - 使用有线网络
   - 调整TCP缓冲区大小
   - 优化路由配置

2. **ESP32优化**
   - 使用PSRAM存储缓冲区
   - 优化任务优先级
   - 启用DMA传输

3. **编码优化**
   - 调整Opus编码参数
   - 使用合适的帧大小
   - 优化比特率设置

## 扩展功能

1. **多格式支持**: 支持MP3、AAC等格式
2. **实时音频**: 支持麦克风实时流传输
3. **音频效果**: 添加音量控制、均衡器
4. **Web界面**: 提供Web控制界面
5. **录音功能**: 支持音频录制和回放

这个系统提供了完整的WebSocket音频流解决方案，具有良好的扩展性和稳定性。
